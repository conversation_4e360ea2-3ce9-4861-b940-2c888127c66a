import { cn } from '@/lib/utils'
import { cva, type VariantProps } from 'class-variance-authority'
import type { ComponentPropsWithoutRef, ReactNode } from 'react'

type AsElement = 'h1' | 'h2' | 'h3' | 'h4' | 'p'

const typographyVariants = cva('font-normal', {
  variants: {
    variant: {
      normal: 'text-light-gray-12 dark:text-dark-gray-12',
      disabled: 'text-light-gray-10 dark:text-dark-gray-10',
      danger: 'text-light-red-12 dark:text-dark-red-12',
    },
    size: {
      h1: 'scroll-m-20 tracking-wide text-4xl md:text-5xl text-balance text-light-lime-11 dark:text-dark-lime-11',
      h2: 'scroll-m-20 tracking-wide text-3xl md:text-4xl text-balance text-light-lime-11 dark:text-dark-lime-11',
      h3: 'scroll-m-20 tracking-wide text-2xl md:text-3xl text-balance text-light-lime-11 dark:text-dark-lime-11',
      h4: 'scroll-m-20 tracking-wide text-xl md:text-2xl text-balance text-light-lime-11 dark:text-dark-lime-11',
      p: 'leading-7 tracking-normal text-pretty',
    },
  },
  compoundVariants: [],
  defaultVariants: {
    variant: 'normal',
  },
})

type TypographyProps<T extends AsElement> = {
  as?: T
  children: ReactNode
  className?: string
} & VariantProps<typeof typographyVariants> &
  Omit<ComponentPropsWithoutRef<T>, 'as' | 'children' | 'className'>

export const Typography = <T extends AsElement = 'p'>({
  as,
  variant,
  children,
  className,
  ...props
}: TypographyProps<T>) => {
  const Component = as || 'p'
  const size = as || 'p'

  return (
    <Component className={cn(typographyVariants({ variant, size }), className)} {...props}>
      {children}
    </Component>
  )
}
