'use client'

import { Typography } from '@/components/typography'
import { CONTACT } from '@/data/contact'
import { SOCIAL_FOOTER } from '@/data/socialFooter'
import { Spline_Sans_Mono } from 'next/font/google'
import Link from 'next/link'
import { useId } from 'react'

const geistMono = Spline_Sans_Mono({
  subsets: ['latin'],
  weight: ['300'],
  display: 'swap',
})

const Footer = () => {
  const id = useId()

  return (
    <footer className={`${geistMono.className} flex flex-col text-xs capitalize my-4`}>
      <div className="flex flex-col md:flex-row justify-between items-start align-middle w-full font-light">
        <div className="grid grid-cols-1 md:grid-cols-2 w-fit space-x-4 items-center justify-center align-middle">
          {SOCIAL_FOOTER.map((data, index) => (
            <Link
              key={`${id}-${data.name}-${index}`}
              href={data.href}
              rel="noreferrer noopener"
              target="_blank"
              className="hover:underline"
            >
              <Typography as="p" size="p" variant="normal">
                {data.name}
              </Typography>
            </Link>
          ))}
        </div>
        <Typography
          as="p"
          size="p"
          variant="disabled"
          className="w-3xs md:w-2xs text-left md:text-center items-center align-middle tracking-tight hidden md:block"
        >
          © 2025 Team 37 - PT Virya Makmur Abadi. All rights reserved.
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-1 w-fit space-x-2 items-center justify-center align-middle">
          {CONTACT.map((data, index) => (
            <Link
              key={`${id}-${data.name}-${index}`}
              href={data.href}
              rel="noreferrer noopener"
              target="_blank"
              className="hover:underline normal-case"
            >
              <Typography as="p" size="p" variant="normal">
                {data.name}
              </Typography>
            </Link>
          ))}
        </div>
        <Typography
          as="p"
          size="p"
          variant="disabled"
          className="w-3xs md:w-2xs text-left md:text-center items-center align-middle tracking-tight md:hidden block"
        >
          © 2025 Team 37 - PT Virya Makmur Abadi. All rights reserved.
        </Typography>
      </div>
    </footer>
  )
}

export default Footer
