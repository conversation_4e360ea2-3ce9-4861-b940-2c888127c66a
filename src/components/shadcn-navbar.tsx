'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Menu, X } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu'
import { Logo } from '@/components/logo'
import { ThemeSwitcherToggle } from '@/components/theme-switcher'
import { NAVBAR_MENU, type NavConfig, type NavItem, isActiveNavItem } from '@/data/navbar-menu'
import { cn } from '@/lib/utils'

interface ShadcnNavbarProps {
  config?: NavConfig
}

export const ShadcnNavbar = ({ config = NAVBAR_MENU }: ShadcnNavbarProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const pathname = usePathname()

  const renderDesktopNavItem = (item: NavItem) => {
    if (item.type === 'link') {
      const isActive = isActiveNavItem(item, pathname)
      return (
        <NavigationMenuItem key={item.id}>
          <Link href={item.href} passHref>
            <NavigationMenuLink
              className={cn(
                navigationMenuTriggerStyle(),
                isActive && 'bg-accent text-accent-foreground',
              )}
              target={item.isExternal ? '_blank' : undefined}
              rel={item.isExternal ? 'noopener noreferrer' : undefined}
            >
              {item.label}
              {item.badge && (
                <span className="ml-2 px-2 py-1 text-xs bg-primary text-primary-foreground rounded-full">
                  {item.badge}
                </span>
              )}
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>
      )
    }

    if (item.type === 'dropdown') {
      const isActive = isActiveNavItem(item, pathname)
      return (
        <NavigationMenuItem key={item.id}>
          <NavigationMenuTrigger className={cn(isActive && 'bg-accent text-accent-foreground')}>
            {item.label}
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
              <div className="row-span-3">
                <NavigationMenuLink asChild>
                  <div className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md">
                    <div className="mb-2 mt-4 text-lg font-medium">{item.label}</div>
                    <p className="text-sm leading-tight text-muted-foreground">
                      {item.description}
                    </p>
                  </div>
                </NavigationMenuLink>
              </div>
              <div className="grid gap-2">
                {item.items.map((subItem) => {
                  if (subItem.type === 'link') {
                    return (
                      <NavigationMenuLink key={subItem.id} asChild>
                        <Link
                          href={subItem.href}
                          className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          target={subItem.isExternal ? '_blank' : undefined}
                          rel={subItem.isExternal ? 'noopener noreferrer' : undefined}
                        >
                          <div className="text-sm font-medium leading-none flex items-center">
                            {subItem.label}
                            {subItem.badge && (
                              <span className="ml-2 px-2 py-1 text-xs bg-primary text-primary-foreground rounded-full">
                                {subItem.badge}
                              </span>
                            )}
                          </div>
                          {subItem.description && (
                            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              {subItem.description}
                            </p>
                          )}
                        </Link>
                      </NavigationMenuLink>
                    )
                  }
                  return null
                })}
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>
      )
    }

    if (item.type === 'button') {
      return (
        <NavigationMenuItem key={item.id}>
          <Button
            variant={item.variant || 'default'}
            onClick={item.onClick}
            disabled={item.isDisabled}
            className="text-sm"
          >
            {item.label}
          </Button>
        </NavigationMenuItem>
      )
    }

    return null
  }

  return (
    <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Brand/Logo */}
          <div className="flex items-center">
            {config.brand.logo ? (
              <Logo
                href={config.brand.href}
                lightLogoSrc={config.brand.logo.light}
                darkLogoSrc={config.brand.logo.dark}
                alt={config.brand.logo.alt}
                width={32}
                height={32}
              />
            ) : (
              <Link href={config.brand.href} className="text-xl font-bold text-primary">
                {config.brand.name}
              </Link>
            )}
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            <NavigationMenu>
              <NavigationMenuList>
                {config.sections.map((section) =>
                  section.items.map((item) => renderDesktopNavItem(item)),
                )}
                {config.cta && renderDesktopNavItem(config.cta)}
              </NavigationMenuList>
            </NavigationMenu>
            <ThemeSwitcherToggle />
          </div>

          {/* Mobile menu button and theme switcher */}
          <div className="md:hidden flex items-center space-x-2">
            <ThemeSwitcherToggle />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="relative"
            >
              <div className="relative w-6 h-6">
                <Menu
                  className={`absolute h-6 w-6 transition-all duration-300 ${
                    isMobileMenuOpen
                      ? 'opacity-0 rotate-90 scale-75'
                      : 'opacity-100 rotate-0 scale-100'
                  }`}
                />
                <X
                  className={`absolute h-6 w-6 transition-all duration-300 ${
                    isMobileMenuOpen
                      ? 'opacity-100 rotate-0 scale-100'
                      : 'opacity-0 -rotate-90 scale-75'
                  }`}
                />
              </div>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t bg-background/95 backdrop-blur-sm">
              <div className="space-y-2">
                {config.sections.map((section) =>
                  section.items.map((item) => {
                    if (item.type === 'link') {
                      const isActive = isActiveNavItem(item, pathname)
                      return (
                        <Link
                          key={item.id}
                          href={item.href}
                          className={cn(
                            'block px-3 py-2 rounded-md text-base font-medium transition-all duration-200 hover:bg-accent hover:text-accent-foreground',
                            isActive && 'bg-accent text-accent-foreground',
                          )}
                          onClick={() => setIsMobileMenuOpen(false)}
                          target={item.isExternal ? '_blank' : undefined}
                          rel={item.isExternal ? 'noopener noreferrer' : undefined}
                        >
                          {item.label}
                          {item.badge && (
                            <span className="ml-2 px-2 py-1 text-xs bg-primary text-primary-foreground rounded-full">
                              {item.badge}
                            </span>
                          )}
                        </Link>
                      )
                    }

                    if (item.type === 'dropdown') {
                      return (
                        <div key={item.id} className="space-y-2">
                          <div className="px-3 py-2 text-sm font-semibold text-muted-foreground">
                            {item.label}
                          </div>
                          {item.items.map((subItem) => {
                            if (subItem.type === 'link') {
                              return (
                                <Link
                                  key={subItem.id}
                                  href={subItem.href}
                                  className="block px-6 py-2 text-sm text-muted-foreground hover:text-accent-foreground hover:bg-accent transition-all duration-200 rounded-md"
                                  onClick={() => setIsMobileMenuOpen(false)}
                                  target={subItem.isExternal ? '_blank' : undefined}
                                  rel={subItem.isExternal ? 'noopener noreferrer' : undefined}
                                >
                                  {subItem.label}
                                  {subItem.badge && (
                                    <span className="ml-2 px-2 py-1 text-xs bg-primary text-primary-foreground rounded-full">
                                      {subItem.badge}
                                    </span>
                                  )}
                                </Link>
                              )
                            }
                            if (subItem.type === 'separator') {
                              return (
                                <div key={subItem.id} className="border-t border-border my-2" />
                              )
                            }
                            return null
                          })}
                        </div>
                      )
                    }

                    if (item.type === 'button') {
                      return (
                        <div key={item.id} className="px-3 py-2">
                          <Button
                            variant={item.variant || 'default'}
                            onClick={() => {
                              setIsMobileMenuOpen(false)
                              item.onClick()
                            }}
                            disabled={item.isDisabled}
                            className="w-full text-sm"
                          >
                            {item.label}
                          </Button>
                        </div>
                      )
                    }

                    return null
                  }),
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
