'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronDown, Menu, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Logo } from '@/components/logo'
import { ThemeSwitcherToggle } from '@/components/theme-switcher'
import { NAVBAR_MENU, type NavConfig, type NavItem, isActiveNavItem } from '@/data/navbar-menu'

interface NavbarProps {
  config?: NavConfig
}

export const Navbar = ({ config = NAVBAR_MENU }: NavbarProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const pathname = usePathname()

  const renderNavItem = (item: NavItem) => {
    if (item.type === 'separator') {
      return <DropdownMenuSeparator key={item.id} />
    }

    if (item.type === 'link') {
      const isActive = isActiveNavItem(item, pathname)
      return (
        <Link
          key={item.id}
          href={item.href}
          className={`px-3 py-2 rounded-md text-sm font-medium transition-colors hover:text-primary ${
            isActive ? 'text-primary bg-primary/10' : 'text-muted-foreground'
          }`}
          target={item.isExternal ? '_blank' : undefined}
          rel={item.isExternal ? 'noopener noreferrer' : undefined}
        >
          {item.label}
          {item.badge && (
            <span className="ml-2 px-2 py-1 text-xs bg-primary text-primary-foreground rounded-full">
              {item.badge}
            </span>
          )}
        </Link>
      )
    }

    if (item.type === 'dropdown') {
      const isActive = isActiveNavItem(item, pathname)
      return (
        <DropdownMenu key={item.id}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className={`group px-3 py-2 text-sm font-medium transition-colors ${
                isActive ? 'text-primary bg-primary/10' : 'text-muted-foreground'
              }`}
            >
              {item.label}
              <ChevronDown className="ml-1 h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            {item.items.map((subItem) => {
              if (subItem.type === 'separator') {
                return <DropdownMenuSeparator key={subItem.id} />
              }
              if (subItem.type === 'link') {
                return (
                  <DropdownMenuItem key={subItem.id} asChild>
                    <Link
                      href={subItem.href}
                      target={subItem.isExternal ? '_blank' : undefined}
                      rel={subItem.isExternal ? 'noopener noreferrer' : undefined}
                      className="flex flex-col items-start"
                    >
                      <div className="flex items-center">
                        {subItem.label}
                        {subItem.badge && (
                          <span className="ml-2 px-2 py-1 text-xs bg-primary text-primary-foreground rounded-full">
                            {subItem.badge}
                          </span>
                        )}
                      </div>
                      {subItem.description && (
                        <span className="text-xs text-muted-foreground">{subItem.description}</span>
                      )}
                    </Link>
                  </DropdownMenuItem>
                )
              }
              return null
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }

    if (item.type === 'button') {
      return (
        <Button
          key={item.id}
          variant={item.variant || 'default'}
          onClick={item.onClick}
          disabled={item.isDisabled}
          className="text-sm"
        >
          {item.label}
        </Button>
      )
    }

    return null
  }

  return (
    <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            {config.brand.logo ? (
              <Logo
                href={config.brand.href}
                lightLogoSrc={config.brand.logo.light}
                darkLogoSrc={config.brand.logo.dark}
                alt={config.brand.logo.alt}
                width={128}
                height={128}
              />
            ) : (
              <Link href={config.brand.href} className="text-xl font-bold text-primary">
                {config.brand.name}
              </Link>
            )}
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            {config.sections.map((section) => section.items.map((item) => renderNavItem(item)))}
            {config.cta && renderNavItem(config.cta)}
            <ThemeSwitcherToggle />
          </div>

          {/* Mobile menu button and theme switcher */}
          <div className="md:hidden flex items-center space-x-2">
            <ThemeSwitcherToggle />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="relative"
            >
              <div className="relative w-6 h-6">
                <Menu
                  className={`absolute h-6 w-6 transition-all duration-300 ${
                    isMobileMenuOpen
                      ? 'opacity-0 rotate-90 scale-75'
                      : 'opacity-100 rotate-0 scale-100'
                  }`}
                />
                <X
                  className={`absolute h-6 w-6 transition-all duration-300 ${
                    isMobileMenuOpen
                      ? 'opacity-100 rotate-0 scale-100'
                      : 'opacity-0 -rotate-90 scale-75'
                  }`}
                />
              </div>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="fixed inset-0 z-50 bg-background/95 backdrop-blur-2xl">
            <div className="h-full overflow-y-auto p-4">
              <div className="space-y-4">
                {/* Home Link */}
                <Link
                  href="/"
                  className="block px-3 py-2 rounded-md text-base font-medium text-foreground hover:bg-primary/10"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Home
                </Link>

                {/* Services Dropdown */}
                <div className="space-y-2">
                  <div className="px-3 py-2 text-sm font-semibold text-muted-foreground">
                    Services
                  </div>
                  <Link
                    href="/services/web-development"
                    className="block px-6 py-2 text-sm text-muted-foreground hover:text-primary"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Web Development
                  </Link>
                  <Link
                    href="/services/branding"
                    className="block px-6 py-2 text-sm text-muted-foreground hover:text-primary"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Branding
                  </Link>
                  <Link
                    href="/services/digital-marketing"
                    className="block px-6 py-2 text-sm text-muted-foreground hover:text-primary"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Digital Marketing
                  </Link>
                  <div className="border-t border-border my-2" />
                  <Link
                    href="/consultation"
                    className="block px-6 py-2 text-sm text-muted-foreground hover:text-primary"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Free Consultation
                    <span className="ml-2 px-2 py-1 text-xs bg-primary text-primary-foreground rounded-full">
                      Free
                    </span>
                  </Link>
                </div>

                {/* Other Links */}
                <Link
                  href="/portfolio"
                  className="block px-3 py-2 rounded-md text-base font-medium text-foreground hover:bg-primary/10"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Portfolio
                </Link>
                <Link
                  href="/about"
                  className="block px-3 py-2 rounded-md text-base font-medium text-foreground hover:bg-primary/10"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  About
                </Link>
                <Link
                  href="/contact"
                  className="block px-3 py-2 rounded-md text-base font-medium text-foreground hover:bg-primary/10"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Contact
                </Link>

                {/* CTA Button */}
                <div className="px-3 py-2">
                  <Button
                    variant="default"
                    className="w-full"
                    onClick={() => {
                      setIsMobileMenuOpen(false)
                      console.log('Get Started clicked')
                    }}
                  >
                    Get Started
                  </Button>
                </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}
