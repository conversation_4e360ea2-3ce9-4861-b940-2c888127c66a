'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { Form, FormControl, FormField, FormItem, FormLabel } from './ui/form'
import { Input } from './ui/input'
import { Textarea } from './ui/textarea'

const allowedChars = /^[a-zA-Z0-9 .,'!?-]+$/
const restrictedEmailDomain = '@gmail.com'

const formSchema = z.object({
  name: z
    .string()
    .min(1, { message: 'Name must be at least 1 character.' })
    .regex(allowedChars, 'Only letters, numbers, spaces are allowed'),
  company: z
    .string()
    .min(1, { message: 'Name must be at least 1 character.' })
    .regex(allowedChars, 'Only letters, numbers, spaces are allowed'),
  role: z
    .string()
    .min(1, { message: 'Name must be at least 1 character.' })
    .regex(allowedChars, 'Only letters, numbers, spaces are allowed'),
  'phone-number': z
    .string()
    .min(1, { message: 'Minimal 10 characters.' })
    .regex(allowedChars, "Only letters, numbers, spaces, and . , ' ! ? - allowed"),
  'work-email': z
    .string()
    .email()
    .min(1, { message: 'Email must be a work email.' })
    .refine((val) => !val.toLowerCase().endsWith(restrictedEmailDomain)),
  inquiry: z.string().min(1, { message: 'Minimal 30 characters' }),
})

export const InquiryForm = ({
  onSubmit,
}: {
  onSubmit: (values: z.infer<typeof formSchema>) => void
}) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      'phone-number': '',
      'work-email': '',
      company: '',
      role: '',
      inquiry: '',
    },
  })

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    console.log(values)
  }

  return (
    <Form {...form}>
      <form
        id="inquiry-form"
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col space-y-6"
      >
        <div>
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="capitalize">Name</FormLabel>
                <FormControl>
                  <Input placeholder="Andrew V." {...field} />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className="flex flex-row gap-4 w-full">
          <FormField
            control={form.control}
            name="phone-number"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="capitalize">Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="+62 812-8787-2257" {...field} />
                </FormControl>
              </FormItem>
            )}
            rules={{ required: true }}
          />
          <FormField
            control={form.control}
            name="work-email"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="capitalize">Work Email</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className="flex flex-row gap-4 w-full">
          <FormField
            control={form.control}
            name="company"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="capitalize">Company</FormLabel>
                <FormControl>
                  <Input placeholder="Team 37" {...field} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="capitalize">Role</FormLabel>
                <FormControl>
                  <Input placeholder="Marketing" {...field} />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div>
          <FormField
            control={form.control}
            name="inquiry"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="capitalize">Describe your needs</FormLabel>
                <FormControl>
                  <Textarea placeholder="I need help with..." {...field} />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  )
}
