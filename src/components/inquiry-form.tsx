import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from './ui/form'
import { Input } from './ui/input'
import { Textarea } from './ui/textarea'
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import Link from 'next/link'
import { Button } from './ui/button'

const allowedChars = /^[a-zA-Z0-9 .,'!?-]+$/
const phoneNumValidation = /^\+?[0-9]+$/

const formSchema = z.object({
  name: z
    .string()
    .min(3, { message: 'Min. 3 character' })
    .max(255, { message: 'Max. 255 characters are allowed' })
    .regex(allowedChars, 'Only letters, numbers, spaces are allowed'),
  company: z
    .string()
    .min(3, { message: 'Min. 3 character' })
    .max(255, { message: 'Min. 255 character' })
    .regex(allowedChars, 'Only letters, numbers, spaces are allowed'),
  role: z
    .string()
    .min(3, { message: 'Min. 3 character' })
    .max(50, { message: 'Min. 50 character' })
    .regex(allowedChars, 'Only letters, numbers, spaces are allowed'),
  'phone-number': z
    .string()
    .min(10, { message: 'Min. 10 characters' })
    .max(13, { message: 'Max. 13 characters' })
    .regex(phoneNumValidation, 'Only numbers are allowed'),
  'work-email': z
    .string()
    .email()
    .min(3, { message: 'Min. 3 characters' })
    .max(255, { message: 'Max. 3 characters' }),
  inquiry: z
    .string()
    .min(10, { message: 'Min. 10 characters' })
    .max(300, { message: 'Max. 300 characters' }),
})

export const InquiryForm = () => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      'phone-number': '',
      'work-email': '',
      company: '',
      role: '',
      inquiry: '',
    },
  })

  const submitForm = () => {
    console.log('form submitted')
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Get Started</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Inquiry Form</DialogTitle>
          <DialogDescription>Please fill out the form below to get started.</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            id="inquiry-form"
            onSubmit={form.handleSubmit(submitForm)}
            className="flex flex-col space-y-6"
          >
            <div>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Andrew V." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex flex-row gap-4 w-full">
              <FormField
                control={form.control}
                name="phone-number"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Phone Number</FormLabel>
                    <FormControl>
                      <Input placeholder="+62 812-8787-2257" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
                rules={{ required: true }}
              />
              <FormField
                control={form.control}
                name="work-email"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Work Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex flex-row gap-4 w-full">
              <FormField
                control={form.control}
                name="company"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Company</FormLabel>
                    <FormControl>
                      <Input placeholder="Team 37" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Role</FormLabel>
                    <FormControl>
                      <Input placeholder="Marketing" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div>
              <FormField
                control={form.control}
                name="inquiry"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Describe your needs</FormLabel>
                    <FormControl>
                      <Textarea maxLength={300} placeholder="I need help with..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        <DialogDescription>
          By clicking submit, you're agree to our{' '}
          <Link className="underline text-light-gray-12 dark:text-dark-gray-12" href="tnc">
            terms and conditions.
          </Link>
        </DialogDescription>
        <DialogFooter>
          <Button type="submit" form="inquiry-form">
            Save changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
