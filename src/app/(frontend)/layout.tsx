import { ThemeProvider } from '@/components/theme-provider'
import './globals.css'
import { Manrope } from 'next/font/google'
import { Toaster } from '@/components/ui/sonner'
import { Navbar } from '@/components/navbar'

export const metadata = {
  description: 'Trusted by SMEs to build, brand, and scale their online presence.',
  title: 'Team 37 | The Digital Partner for Growing Brands ',
}

const manrope = Manrope({
  subsets: ['latin'],
  weight: ['300', '400', '500'],
  display: 'swap',
})

export default async function RootLayout(props: { children: React.ReactNode }) {
  const { children } = props

  return (
    <html lang="en" className={manrope.className} suppressHydrationWarning>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem={true}
          disableTransitionOnChange
        >
          <Navbar />
          <main className="bg-light-lime-1 dark:bg-dark-lime-1 w-full mx-auto min-h-screen pt-16 px-4 sm:px-6 lg:px-32">
            <Toaster richColors closeButton position="bottom-right" duration={5000} />
            {children}
          </main>
        </ThemeProvider>
      </body>
    </html>
  )
}
