@import "tailwindcss";
@import "tw-animate-css";
@import "@radix-ui/colors/red.css";
@import "@radix-ui/colors/red-dark.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Color styles */
  --color-des: #FC60A8;
  --color-cre: #FF7F11;
  --color-dev: #2CDA9D;
  --color-blue-1: #F7F8F9;
  --color-blue-2: #F0F4FB;
  --color-blue-3: #E5ECFB;
  --color-blue-4: #D5E4FF;
  --color-blue-5: #C3D9FF;
  --color-blue-6: #ADCBFF;
  --color-blue-7: #95B8FE;
  --color-blue-8: #729EF7;
  --color-blue-9: #0E4BE6;
  --color-blue-10: #0C40C6;
  --color-blue-11: #194FD5;
  --color-blue-12: #132C65;
  --color-dark-lime-1: #070B04;
  --color-dark-lime-2: #13190C;
  --color-dark-lime-3: #1D290F;
  --color-dark-lime-4: #263710;
  --color-dark-lime-5: #2F4512;
  --color-dark-lime-6: #395414;
  --color-dark-lime-7: #456418;
  --color-dark-lime-8: #527819;
  --color-dark-lime-9: #ADFF00;
  --color-dark-lime-9: #ADFF00;
  --color-dark-lime-10: #AAF23D;
  --color-dark-lime-11: #A7EE37;
  --color-dark-lime-12: #D6FCAC;
  --color-light-lime-1: #fbfdf9;
  --color-light-lime-2: #f6fbf1;
  --color-light-lime-3: #e5fbcf;
  --color-light-lime-4: #d4f7b0;
  --color-light-lime-5: #c4ee95;
  --color-light-lime-6: #b2e17c;
  --color-light-lime-7: #9dcf5f;
  --color-light-lime-8: #81bb1f;
  --color-light-lime-9: #adff00;
  --color-light-lime-10: #a4f500;
  --color-light-lime-11: #538000;
  --color-light-lime-12: #2e4312;
  --color-light-gray-1: #F7F7F7;
  --color-light-gray-2: #F3F3F3;
  --color-light-gray-3: #E9E9E9;
  --color-light-gray-4: #E1E1E1;
  --color-light-gray-5: #DADADA;
  --color-light-gray-6: #D1D1D1;
  --color-light-gray-7: #C6C6C6;
  --color-light-gray-8: #B3B3B3;
  --color-light-gray-9: #868686;
  --color-light-gray-10: #7B7B7B;
  --color-light-gray-11: #5D5D5D;
  --color-light-gray-12: #202020;
  --color-dark-gray-1: #090909;
  --color-dark-gray-2: #181818;
  --color-dark-gray-3: #222222;
  --color-dark-gray-4: #292929;
  --color-dark-gray-5: #313131;
  --color-dark-gray-6: #3A3A3A;
  --color-dark-gray-7: #484848;
  --color-dark-gray-8: #606060;
  --color-dark-gray-9: #6E6E6E;
  --color-dark-gray-10: #7B7B7B;
  --color-dark-gray-11: #B4B4B4;
  --color-dark-gray-12: #EEEEEE;
  --color-neutral-white: #FFFFFF;
  --color-neutral-black: #000000;
  --color-light-red-1: var(--red-1);
  --color-light-red-2: var(--red-2);
  --color-light-red-3: var(--red-3);
  --color-light-red-4: var(--red-4);
  --color-light-red-5: var(--red-5);
  --color-light-red-6: var(--red-6);
  --color-light-red-7: var(--red-7);
  --color-light-red-8: var(--red-8);
  --color-light-red-9: var(--red-9);
  --color-light-red-10: var(--red-10);
  --color-light-red-11: var(--red-11);
  --color-light-red-12: var(--red-12);
  --color-dark-red-1: var(--red-dark-1);
  --color-dark-red-2: var(--red-dark-2);
  --color-dark-red-3: var(--red-dark-3);
  --color-dark-red-4: var(--red-dark-4);
  --color-dark-red-5: var(--red-dark-5);
  --color-dark-red-6: var(--red-dark-6);
  --color-dark-red-7: var(--red-dark-7);
  --color-dark-red-8: var(--red-dark-8);
  --color-dark-red-9: var(--red-dark-9);
  --color-dark-red-10: var(--red-dark-10);
  --color-dark-red-11: var(--red-dark-11);
  --color-dark-red-12: var(--red-dark-12);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}