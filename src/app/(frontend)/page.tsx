import { InquiryForm } from '@/components/form'
import Footer from '@/components/footer'
import Header from '@/components/header'
import { Typography } from '@/components/typography'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { DialogTrigger } from '@radix-ui/react-dialog'
import Link from 'next/link'

const HomePage = () => {
  const handleSubmit = (data: any) => {
    console.log('[SUBMIT] Form Data:', data)
    // You can integrate fetch/post request here (e.g. to Supabase, API route, etc)
  }
  return (
    <div className="flex flex-col justify-between h-screen w-full">
      <Header />
      <div className="flex flex-col justify-between items-center align-middle">
        <div className="flex flex-col justify-center items-center align-middle">
          <div className="flex flex-col justify-center items-center align-middle w-auto sm:w-xl lg:w-3xl gap-8">
            <Typography as="h1" size="h1" className="w-full text-center">
              The Digital Partner for Growing Brands
            </Typography>
            <Dialog>
              <DialogTrigger asChild>
                <Button>Get Started</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Inquiry Form</DialogTitle>
                  <DialogDescription>
                    Please fill out the form below to get started.
                  </DialogDescription>
                </DialogHeader>
                <InquiryForm />
                <DialogDescription>
                  By clicking submit, you're agree to our{' '}
                  <Link className="underline text-light-gray-12 dark:text-dark-gray-12" href="tnc">
                    terms and conditions.
                  </Link>
                </DialogDescription>
                <DialogFooter>
                  <Button type="submit">Save changes</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            <div className="flex flex-col justify-center items-center align-middle text-center">
              <Typography as="p" size="p" variant="disabled">
                Trusted by SMEs to build, brand, and scale their online presence.
              </Typography>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}

export default HomePage
