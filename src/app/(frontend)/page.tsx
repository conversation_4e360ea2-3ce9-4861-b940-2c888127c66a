'use client'

import { InquiryForm } from '@/components/inquiry-form'
import Footer from '@/components/footer'
import Header from '@/components/header'
import { Typography } from '@/components/typography'
import { Navbar } from '@/components/navbar'

const HomePage = () => {
  return (
    <div className="flex flex-col justify-between h-screen w-full">
      <Navbar />
      <div className="flex flex-col justify-between items-center align-middle">
        <div className="flex flex-col justify-center items-center align-middle">
          <div className="flex flex-col justify-center items-center align-middle w-auto sm:w-xl lg:w-3xl gap-8">
            <Typography as="h1" size="h1" className="w-full text-center">
              The Digital Partner for Growing Brands
            </Typography>
            <InquiryForm />
            <div className="flex flex-col justify-center items-center align-middle text-center">
              <Typography as="p" size="p" variant="disabled">
                Trusted by SMEs to build, brand, and scale their online presence.
              </Typography>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}

export default HomePage
