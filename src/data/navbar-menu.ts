import type { LucideIcon } from 'lucide-react'

// Base navigation item interface
interface BaseNavItem {
  id: string
  label: string
  description?: string
  icon?: LucideIcon
  isExternal?: boolean
  isDisabled?: boolean
  badge?: string
}

// Simple link navigation item
export interface NavLink extends BaseNavItem {
  type: 'link'
  href: string
}

// Dropdown navigation item with sub-items
export interface NavDropdown extends BaseNavItem {
  type: 'dropdown'
  items: NavItem[]
}

// Button navigation item (for CTAs)
export interface NavButton extends BaseNavItem {
  type: 'button'
  onClick: () => void
  variant?: 'default' | 'secondary' | 'outline' | 'ghost' | 'destructive'
}

// Separator for visual grouping
export interface NavSeparator {
  type: 'separator'
  id: string
}

// Union type for all navigation items
export type NavItem = NavLink | NavDropdown | NavButton | NavSeparator

// Navigation section for grouping items
export interface NavSection {
  id: string
  title?: string
  items: NavItem[]
}

// Main navigation configuration
export interface NavConfig {
  brand: {
    name: string
    href: string
    logo?: {
      light: string
      dark: string
      alt: string
    }
  }
  sections: NavSection[]
  cta?: NavButton
}

// Example navbar data
export const NAVBAR_MENU: NavConfig = {
  brand: {
    name: 'Team 37',
    href: '/',
    logo: {
      light: '/light-team37-logo.svg',
      dark: '/dark-team37-logo.svg',
      alt: 'Team 37 logo',
    },
  },
  sections: [
    {
      id: 'main-nav',
      items: [
        {
          id: 'home',
          type: 'link',
          label: 'Home',
          href: '/',
          description: 'Go to homepage',
        },
        {
          id: 'services',
          type: 'dropdown',
          label: 'Services',
          description: 'Our digital services',
          items: [
            {
              id: 'web-development',
              type: 'link',
              label: 'Web Development',
              href: '/services/web-development',
              description: 'Custom web applications and websites',
            },
            {
              id: 'branding',
              type: 'link',
              label: 'Branding',
              href: '/services/branding',
              description: 'Brand identity and design',
            },
            {
              id: 'digital-marketing',
              type: 'link',
              label: 'Digital Marketing',
              href: '/services/digital-marketing',
              description: 'SEO, Ads, and social media marketing',
            },
          ],
        },
        {
          id: 'portfolio',
          type: 'link',
          label: 'Portfolio',
          href: '/portfolio',
          description: 'View our work',
        },
        {
          id: 'about',
          type: 'link',
          label: 'About',
          href: '/about',
          description: 'Learn more about Team 37',
        },
        {
          id: 'contact',
          type: 'link',
          label: 'Contact',
          href: '/contact',
          description: 'Get in touch with us',
        },
      ],
    },
  ],
  cta: {
    id: 'get-started',
    type: 'button',
    label: 'Get Started',
    description: 'Start your project with us',
    variant: 'default',
    onClick: () => {
      // Handle CTA click - could open a modal, navigate, etc.
      console.log('Get Started clicked')
    },
  },
}

// Helper functions for working with navigation data
export const getNavItemById = (id: string, config: NavConfig): NavItem | undefined => {
  for (const section of config.sections) {
    for (const item of section.items) {
      if (item.id === id) return item
      if (item.type === 'dropdown') {
        const found = item.items.find((subItem) => subItem.id === id)
        if (found) return found
      }
    }
  }
  return undefined
}

export const getAllNavLinks = (config: NavConfig): NavLink[] => {
  const links: NavLink[] = []

  for (const section of config.sections) {
    for (const item of section.items) {
      if (item.type === 'link') {
        links.push(item)
      } else if (item.type === 'dropdown') {
        for (const subItem of item.items) {
          if (subItem.type === 'link') {
            links.push(subItem)
          }
        }
      }
    }
  }

  return links
}

export const isActiveNavItem = (item: NavItem, currentPath: string): boolean => {
  if (item.type === 'link') {
    return item.href === currentPath || currentPath.startsWith(`${item.href}/`)
  }
  if (item.type === 'dropdown') {
    return item.items.some((subItem) => isActiveNavItem(subItem, currentPath))
  }
  return false
}
